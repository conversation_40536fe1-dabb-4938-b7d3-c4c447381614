import { expect, test } from 'vitest';
import { APICaller } from '../src';

// import { APIEdgeCommandTelegramMessageResponseSchema } from '../src/types';

test('Test API Caller ID', async () => {
  const id = APICaller.genId();
  console.log(`Random API Caller ID: ${id}`);
  expect(id.startsWith('ose')).toBe(true);
});

test.skip('Test API Caller', async () => {
  // /api/meta
  const caller2 = new APICaller({
    bikaBasePath: 'https://bika.ai',
  });
  const res2 = await caller2.apiClient.get('/meta');
  expect(res2.data.version).toBeDefined();

  const caller3 = new APICaller({
    bikaBasePath: 'https://toolsdk.ai',
  });
  const res3 = await caller3.apiClient.get('/meta');
  expect(res3.data.version).toBeDefined();

  const caller6 = new APICaller({
    bikaBasePath: 'https://dev.bika.ai',
  });
  const res6 = await caller6.apiClient.get('/meta');
  expect(res6.data.version).toBeDefined();

  const caller4 = new APICaller({
    bikaBasePath: 'https://staging.bika.ai',
  });
  const res4 = await caller4.apiClient.get('/meta');
  expect(res4.data.version).toBeDefined();

  // ping ms测试
  const lattency = await caller2.pingLattency();
  expect(lattency).toBeLessThan(5000);

  console.log('ping to bika.ai:', lattency, 'ms');

  // 边缘配置服务测试
  const resConfig = await caller2.getEdgeConfig();
  expect(resConfig.url).toBe('https://bika.ai');

  // const res2 = await caller.httpClient.callEdge({
  //   edgeToken: process.env.EDGE_TOKEN!,
  //   data: {
  //     command: 'health-check',
  //   },
  // });
  // expect(res2.code).toBe(200);

  // // /api/edge simulate a telegram bot
  // const edgeRes = await caller.httpClient.callEdge({
  //   edgeToken: process.env.EDGE_TOKEN!,
  //   data: {
  //     command: 'telegram:message',
  //     message: {
  //       text: 'Hello, world!',
  //     },
  //     chatId: 'http-test',
  //     userId: 'http-test-user',
  //   },
  // });

  // expect(edgeRes.code).toBe(200);
  // const tgParams = APIEdgeCommandTelegramMessageResponseSchema.parse(edgeRes.data);
  // expect(tgParams.message.length).toBeGreaterThan(0); // 回复的消息字符串
});

test('Test ZodError formatting', () => {
  // Test the formatZodErrorMessage function by creating a mock TRPCClientError
  // with ZodError data in the message field (as JSON string)
  const zodErrorIssues = [
    {
      code: 'too_small',
      minimum: 1,
      type: 'number',
      inclusive: true,
      exact: false,
      message: 'file size must be greater than 0',
      path: ['size'],
    },
  ];

  // Create a mock TRPCClientError with the correct structure
  const mockTRPCError = {
    data: {
      code: 1000,
      path: 'attachment.getPresignedPut',
      httpStatus: 400,
      message: JSON.stringify(zodErrorIssues),
    },
    message: 'Validation error',
  } as TRPCClientError;

  // Create an APICaller instance with a mock toast function to capture the formatted message
  let capturedMessage = '';
  const mockToast = {
    error: (message: string) => {
      capturedMessage = message;
    },
  };

  const caller = new APICaller({
    bikaBasePath: 'https://test.example.com',
    toast: mockToast as any,
  });

  // Simulate the error handling by calling the onError handler directly
  const queryCache = caller.reactQueryClient.getQueryCache();
  const onErrorHandler = (queryCache as any).config.onError;

  if (onErrorHandler) {
    onErrorHandler(mockTRPCError, { meta: {} });
  }

  // Verify that the captured message is a prettified version of the ZodError
  console.log('Captured message:', capturedMessage);
  console.log('Raw JSON:', JSON.stringify(zodErrorIssues));

  expect(capturedMessage).toBeDefined();
  expect(capturedMessage).toContain('file size must be greater than 0');
});
